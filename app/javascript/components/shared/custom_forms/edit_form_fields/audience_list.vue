<template>
  <div
    v-if="field.type == 'audience_list'"
    class="row mt-2"
  >
    <div class="col-md-5">
      {{ field.label }}
    </div>
    <div 
      v-if="fieldName == 'followers'"
      class="col-md-7 d-flex"
    >
      <input
        :id="'guestOption'"
        type="radio"
        :checked="true"
      >
      <label
        :for="'guestOption'"
        class="font-weight-normal ml-1 mb-0"
      >
        {{ options[1].name }}
      </label>
    </div>
    <div
      v-else-if="options && options.length > 0"
      class="col-md-7 d-flex"
    >
      <div
        v-for="(opt, index) in options"
        :key="index"
        :class="{'margin-left': index >= 1}"
        class="d-flex align-items-center col-6 px-0"
      >
        <input
          :id="index"
          type="radio"
          :value="opt.value"
          :checked="value === opt.value || (!value && opt.value === 'teammates')"
          @input="emitValue"
        >
        <label
          :for="index"
          class="font-weight-normal ml-1 mb-0"
        >
          {{ opt.name }}
        </label>
      </div>
    </div>
  </div>
</template>

<script>
import customFormHelper from "mixins/custom_form_helper";

export default {
  mixins: [ customFormHelper ],
  props: {
    field: {
      type: Object,
      required: true,
    },
    value: {
      type: Object,
      default: null,
    },
    fieldName: {
      type: String,
      default: '',
    },
  },
  computed: {
    options() {
      if (typeof this.field.options === 'string') {
        return JSON.parse(this.field.options);
      };
      return this.field.options;
    },
  },
  methods: {
    emitValue(value) {
      const idx = parseInt(value.currentTarget.id, 10);
      this.$emit("change-audience", this.field.options[idx]);
    },
  },
};
</script>

<style lang="scss" scoped>
.margin-left {
  margin-left: 0.75rem;
}
</style>
