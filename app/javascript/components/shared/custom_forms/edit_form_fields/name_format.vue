<template>
  <div
    v-if="field.type == 'name_format'"
    class="row mt-2"
  >
    <div class="col-md-5 mb-2">
      {{ field.label }}
      <i
        v-tooltip="'Select name format, which you would like all the names to appear in options.'"
        class="nulodgicon-help-circled text-muted font-weight-semi-bold clickable"
      />
    </div>
    <div
      v-if="sortListVal"
      class="col-md-7 mb-2 row"
    >
      <div
        v-for="(opt, index) in options"
        :key="index"
        class="d-flex align-items-center col-6"
      >
        <input
          :id="`name-format-${_uid}-${index}`"
          v-model="sortListVal[1]"
          type="radio"
          :name="`name-format-${_uid}`"
          :value="opt"
          :class="{'margin-left': index >= 1}"
          @change="emitValue()"
        >
        <label
          v-tooltip="{ content: currentUserName(opt).length > 16 ? currentUserName(opt) : '' }"
          :for="`name-format-${_uid}-${index}`"
          class="mb-0 font-weight-normal ml-1 truncate w-100"
        >
          {{ currentUserName(opt) }}
        </label>
      </div>
    </div>
  </div>
</template>

<script>
import customFormHelper from "mixins/custom_form_helper";

export default {
 
  mixins: [customFormHelper],
  props: {
    field: {
      type: Object,
      required: true,
    },
    value: {
      type: String,
      required: false,
      default: '',
    },
    sortList: {
      type: Array,
      required: false,
      default: () => ["Ascending", "First Name, Last Name"],
    },
  },
  data() {
    return {
      optionsAvaliable: false,
    };
  },
  computed: {
    sortListVal(){
      return this.sortList;
    },
    options() {
      if (typeof this.field.options === "string") {
        return JSON.parse(this.field.options);
      };
      return this.field.options;
    },
  },
  methods: {
    currentUserName(option){
      if (option === "Last Name, First Name") {
        return `${this.$currentUserLastName} ${this.$currentUserFirstName}`;
      } 
        return  `${this.$currentUserFirstName} ${this.$currentUserLastName}`
      ;
    },
    emitValue() {
      if (this.sortList[1] === this.field.options[0]) {
        this.$emit("change-name-format", this.field.options[0]);
      } else {
        this.$emit("change-name-format", this.field.options[1]);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.margin-left {
  margin-left: 0.75rem;
}
</style>
