<template>
  <div
    v-if="field.type == 'smart_list'"
    class="row"
  >
    <div class="col-md-5 mb-2">
      {{ field.label }}
    </div>
    <div class="col-md-7 mb-2">
      <component
        :is="`${toHyphenCase(field.fieldAttributeType)}Input`"
        :id="field.label"
        ref="field"
        :field="field"
        :value="fieldValue"
        include-current
        @input="emitValue"
      />
    </div>
  </div>
</template>

<script>
  import strings from 'mixins/string.js';
  import customFormHelper from "mixins/custom_form_helper.js";
  import * as formViewerFields from "../form_viewer_fields/index.js";

  export default {
    mixins: [customFormHelper, strings],
    props: {
      field: {
        type: Object,
        required: true
      },
      value: {
        required: false
      }
    },
    components: {
      ...formViewerFields
    },
    computed: {
      fieldValue: {
        get() {
          if (typeof this.value === 'string') {
            return JSON.parse(this.value);
          }
          return this.value;
        },
        set(newValue) {
          this.$emit('input', newValue);
        }
      },
    },
    methods: {
      onWorkspaceChange() {
        this.emitValue(this.value);
      },
      emitValue(val) {
        this.$emit('update:value', val);
      }
    },

  }
</script>
