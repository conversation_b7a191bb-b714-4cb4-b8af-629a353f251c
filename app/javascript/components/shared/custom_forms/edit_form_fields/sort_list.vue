<template>
  <div
    v-if="field.type == 'sort_list'"
    class="row mt-2"
  >
    <div class="col-md-5">
      {{ field.label }}
    </div>
    <div
      v-if="sortListVal"
      class="col-md-7 d-flex"
    >
      <div
        v-for="(opt, index) in options"
        :key="index"
        class="d-flex align-items-center col-6 px-0"
      >
        <input
          :id="`sort-preference-${clonedAttribute.orderPosition}-${index}`"
          v-model="sortListVal[0]"
          type="radio"
          :name="`sort-preference-${clonedAttribute.orderPosition}`"
          :value="opt"
          :class="{'margin-left': index >= 1}"
          @change="emitValue()"
        >
        <label
          :for="`sort-preference-${clonedAttribute.orderPosition}-${index}`"
          class="mb-0 font-weight-normal ml-1"
        >
          {{ opt }}
        </label>
      </div>
    </div>
  </div>
</template>

<script>
import customFormHelper from "mixins/custom_form_helper";

export default {
  mixins: [customFormHelper],
  props: {
    field: {
      type: Object,
      required: true,
    },
    sortList: {
      required: false,
      default: () => ["Ascending", "First Name, Last Name"],
    },
    clonedAttribute: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      optionsAvaliable: false,
    };
  },
  computed: {
    sortListVal(){
      return this.sortList;
    },
    options() {
      if (typeof this.field.options === 'string') {
        return JSON.parse(this.field.options);
      };
      return this.field.options;
    },
  },
  methods: {
    emitValue() {
      if (this.sortList[0] === this.field.options[0]) {
        this.$emit("change-preference", this.field.options[0]);
      } else {
        this.$emit("change-preference", this.field.options[1]);
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.margin-left {
  margin-left: 0.75rem;
}
</style>
