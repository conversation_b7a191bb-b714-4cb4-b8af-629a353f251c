<template>
  <div>
    <h6 class="mb-4">
      Drag &amp; Drop Elements
    </h6>
    <div v-if="isHelpdeskModule">
      <div class="d-flex justify-content-between bg-very-fair not-as-small rounded-pill px-3 py-1 mb-3">
        <div class="font-weight">
          Suggested Fields
        </div>
      </div>
      <draggable
        :group="{ name: 'fieldOptions', pull: 'clone', put: false }"
        :list="suggestedFields"
        :sort="false"
        :scroll-sensitivity="50"
        :scroll-speed="50"
      >
        <field-palette-item
          v-for="(element, idx) in suggestedFields"
          :key="idx"
          :label="element.label"
          :icon-text-content="element.iconTextContent"
          :icon-class="element.iconClass"
        />
      </draggable>
      <hr class="my-3">
    </div>
    <div class="d-flex justify-content-between bg-very-fair not-as-small rounded-pill px-3 py-1 mb-3">
      All Fields
    </div>

    <div>
      <draggable
        :group="{ name: 'fieldOptions', pull: 'clone', put: false }"
        :list="filteredPaletteItems"
        :sort="false"
        :scroll-sensitivity="50"
        :scroll-speed="50"
      >
        <field-palette-item
          v-for="(element, idx) in filteredPaletteItems"
          :key="idx"
          :label="element.label"
          :icon-text-content="element.iconTextContent"
          :icon-class="element.iconClass"
        />
      </draggable>

      <draggable
        :group="{ name: 'fieldOptions', pull: 'clone', put: false }"
        :list="filteredSmartPaletteItems"
        :sort="false"
        :scroll-sensitivity="50"
        :scroll-speed="50"
      >
        <field-palette-item
          v-for="(element, idx) in filteredSmartPaletteItems"
          :key="idx + filteredPaletteItems.length"
          :label="element.label"
          :icon-text-content="element.iconTextContent"
          :icon-class="element.iconClass"
          :img-src="element.imgSrc"
        />
      </draggable>
    </div>
  </div>
</template>

<script>
  import customFormHelper from 'mixins/custom_form_helper';
  import Draggable from 'vuedraggable';
  import _find from "lodash/find";
  import _camelCase from "lodash/camelCase";
  import permissionsHelper from 'mixins/permissions_helper';
  import FieldPaletteItem from "./field_palette_item.vue";
  import subscription from '../../../stores/mixins/subscription';

  const DEFAULT_TAGS = ["SaaS", "Business Service", "Business Advertising", "Infrastructure"];

  export default {
    $_veeValidate: {
      validator: 'new',
    },
    components: {
      Draggable,
      FieldPaletteItem,
    },
    mixins: [ customFormHelper, subscription, permissionsHelper ],
    props: {
      companyModule: {
        type: String,
        default: "helpdesk",
      },
    },

    data() {
      return {
        allPaletteItems: [
          { name: "text", iconTextContent: "Aa", fieldAttributeType: "text", required: false, note: "", label: "Text", fieldPosition: { position: "left" }, isDragged: true },
          { name: "static_text", iconTextContent: "Aa", fieldAttributeType: "static_text", required: false, label: "Static Text", fieldPosition: { position: "left" }, isDragged: true },
          { name: "text_area", iconTextContent: "Aa", fieldAttributeType: "text_area", required: false, note: "", label: "Text Area", fieldPosition: { position: "left" }, isDragged: true },
          { name: "rich_text", iconTextContent: "Aa", fieldAttributeType: "rich_text", required: false, note: "", label: "Rich Text", fieldPosition: { position: "left" }, isDragged: true },
          { name: "number", iconTextContent: "42", fieldAttributeType: "number", required: false, note: "", label: "Number", fieldPosition: { position: "right" }, isDragged: true },
          { name: "list", iconClass: "genuicon-list-ul", fieldAttributeType: "list", required: false, note: "", listType: "", label: "List", fieldPosition: { position: "right" }, options: [], isDragged: true },
          { name: "status", iconClass: "genuicon-check-square-o", fieldAttributeType: "status", required: false, note: "", label: "Status", fieldPosition: { position: "right" }, options: [{ name: "Open", color:"#2ECC71" }, { name: "In Progress", color: "#5DADE2" }, { name: "Closed", color: "#626567" }], isDragged: true },
          { name: "priority", iconClass: "genuicon-flag-o", fieldAttributeType: "priority", required: false, note: "", label: "Priority", fieldPosition: { position: "right" }, options: [{name: "low", color: "#ffd700"}, {name: "medium", color: "#ffa500"}, {name: "high", color: "#ff0000"}], isDragged: true },
          // { name: "Universal Link", iconClass: "nulodgicon-ios-world", fieldAttributeType: "universal_link", required: false, note: "", label: "Add a label", fieldPosition: { position: "right" } },
          { name: "attachments", iconClass: "nulodgicon-upload", fieldAttributeType: "attachment", required: false, note: "", label: "Attachments", fieldPosition: { position: "main" }, isDragged: true },
          { name: "avatar", iconClass: "genuicon-pencil-square-o", fieldAttributeType: "avatar", required: false, note: "", label: "Avatar", fieldPosition: { position: "right" }, isDragged: true },
          { name: "date", iconClass: "genuicon-calendar-o", fieldAttributeType: "date", required: false, note: "", label: "Date", fieldPosition: { position: "right" }, isDragged: true },
          { name: "phone", iconClass: "nulodgicon-android-call", fieldAttributeType: "phone", required: false, note: "", label: "Phone", fieldPosition: { position: "right" }, isDragged: true },
          { name: "email_address", iconClass: "nulodgicon-email", fieldAttributeType: "email", required: false, label: "Email Address", fieldPosition: { position: "right" }, isDragged: true },
          { name: "category", iconClass: "genuicon-list-ul", fieldAttributeType: "category", required: false, note: "", label: "Category", fieldPosition: { position: "right" }, isDragged: true },
          { name: "department", iconClass: "genuicon-list-ul", fieldAttributeType: "department", required: false, note: "", label: "Department", fieldPosition: { position: "right" }, isDragged: true },
          { name: "tag", iconClass: "genuicon-check-square-o", fieldAttributeType: "tag", required: false, note: "", label: "Tag", options: DEFAULT_TAGS, fieldPosition: { position: "right" }, isDragged: true },
          { name: "radio_button", iconClass: "genuicon-circle-line", fieldAttributeType: "radio_button", required: false, label: "Radio Button", fieldPosition: { position: "right" }, options: [], isDragged: true },
          { name: "checkbox", iconClass: "nulodgicon-android-checkbox-outline-blank", fieldAttributeType: "checkbox", required: false, label: "Checkbox", fieldPosition: { position: "right" }, options: [], isDragged: true },
          // Smart Palette Items
          { name: "people_list", iconClass: "nulodgicon-person-stalker", fieldAttributeType: "people_list", required: false, private: true, note: "", label: "People List", fieldPosition: { position: "right" }, options: [], sortList: ["Ascending", "First Name, Last Name"], isDragged: true, audience: "teammates" },
          { name: "asset_list", imgSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-assets.svg", fieldAttributeType: "asset_list", required: false, private: true, note: "", label: "Asset List", fieldPosition: { position: "right" }, isDragged: true },
          { name: "contract_list", imgSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-contracts.svg", fieldAttributeType: "contract_list", required: false, private: true, note: "", label: "Contract List", fieldPosition: { position: "right" }, isDragged: true },
          { name: "vendor_list", imgSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-vendors.svg", fieldAttributeType: "vendor_list", required: false, private: true, note: "", label: "Vendor List", fieldPosition: { position: "right" }, isDragged: true },
          { name: "telecom_list", imgSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-telecom.svg", fieldAttributeType: "telecom_list", required: false, private: true, note: "", label: "Telecom List", fieldPosition: { position: "right" }, isDragged: true },
          { name: "location_list", iconClass: "nulodgicon-location", fieldAttributeType: "location_list", required: false, private: true, note: "", label: "Location List", fieldPosition: { position: "right" }, isDragged: true },
        ],
      };
    },
    computed: {
      suggestedFields() {
        return this.allPaletteItems.filter(
          (item) => item.name === "category" || item.name === "attachments"
        );
      },
      isHelpdeskModule() {
        return this.companyModule === "helpdesk";
      },
      filteredPaletteItems() {
        const filteredItems = [];
        if (this.companyModule) {
          const paletteItems = this[_camelCase(`${this.companyModule}PalleteItems`)].paletteItems.concat(this.commonPaletteItems);
          paletteItems.forEach((item) => {
            const paletteItem = item === "email" ? "email_address" : item;
            filteredItems.push(_find(this.allPaletteItems, { name: paletteItem }));
          });
        }
        return filteredItems;
      },
      filteredSmartPaletteItems() {
        const filteredItems = [];
        if (this.companyModule) {
          const paletteItems = this[_camelCase(`${this.companyModule}PalleteItems`)].smartPaletteItems;
          paletteItems.forEach((item) => {
            filteredItems.push(_find(this.allPaletteItems, { name: item }));
          });
        }
        return this.filterSubscriptions(filteredItems);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .move-cursor {
    cursor: move;
  }

  .tips-icon {
    width: 1rem;
  }
</style>
