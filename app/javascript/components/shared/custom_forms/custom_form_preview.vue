<template>
  <div>
    <div
      v-for="(field, index) in currentForm.formFields"
      :id="`field-${field.id || field.orderPosition || index}`"
      :key="`field-${field.id || field.orderPosition || index}`"
      :ref="`field_wrapper${field.id || field.orderPosition || index}`"
      class="scroll-elem"
    >
      <field-previewer-input
        v-if="canShowField(field)"
        :ref="`field${field.id}`"
        :field="field"
        :value="field.value"
        class="form-group"
        :data-tc="field.name"
      />
    </div>
  </div>
</template>
  
<script>
  import FieldPreviewerInput from './field_previewer_input.vue';

  export default {
    components: {
      FieldPreviewerInput,
    },
    props: {
      isTicketPortal: {
        type: Boolean,
        default: false,
      },
      form: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        currentForm: this.form,
      };
    },
    methods: {
      canShowField(field) {
        if (this.isTicketPortal) {
          return !field.private;
        };
        return true;
      },
    },
  };
</script>
